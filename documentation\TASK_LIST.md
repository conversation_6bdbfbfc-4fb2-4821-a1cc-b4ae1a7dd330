# LuminariMUD Development Task List

## CODER TASKS

## Memory Leak Fixes (Valgrind Analysis - 2025-07-26)

### Critical Priority - Crashes/Segfaults
- [x] **Attempt to fix use-after-free in spell_prep.c:93-96** - clear_prep_queue_by_class() accessing freed memory causing SIGSEGV
  - Invalid read at 0xbbbbbbbbbbbbbbcb indicates use of freed memory filled with 0xBB pattern
  - Called from destroy_spell_prep_queue() -> free_char() -> show_account_menu()
  - **Fix Attempted (2025-07-26)**: Added NULL checks in destroy_spell_prep_queue(), destroy_innate_magic_queue(), destroy_spell_collection(), and destroy_known_spells() to prevent accessing freed memory
  - **Status**: Unverified - requires testing to confirm fix resolves the issue

### High Priority - Significant Memory Leaks
- [x] **Attempt to fix strdup leak in players.c:877** - load_char() allocating string without freeing (7 bytes lost)
  - **Fix Attempted (2025-07-26)**: Added freeing of GET_CRAFT(ch).ex_description in reset_current_craft() and free_char()
  - **Files Modified**: crafting_new.c:2326,3266, db.c:5359
  - **Status**: Unverified - requires testing to confirm fix resolves the issue
- [ ] **Attempt to fix object memory leaks in house.c:215-219** - Recursive House_save() calls creating objects without freeing
  - 800 bytes lost in single blocks
  - 1,600 bytes lost in 2 blocks
  - Related to objsave_save_obj_record_db() -> read_object() allocation pattern
  - **Status**: Needs further investigation - may be false positive or complex ownership issue
- [x] **Attempt to fix memory leak in hlquest.c:925** - boot_the_quests() leaking 15,744 bytes in 656 blocks
  - **Fix Attempted (2025-07-26)**: Added default case to free orphaned quest commands when inner[0] is neither 'I' nor 'O'
  - **Files Modified**: hlquest.c:987-991
  - **Status**: Unverified - requires testing to confirm fix resolves the issue

### Medium Priority - Multiple Small Leaks
- [x] **Attempt to fix strdup leaks in assign_wpn_armor.c:922** - setweapon() function allocating strings without freeing
  - Multiple 4-8 byte leaks across different weapon loading calls
  - **Status**: Reviewed (2025-07-26) - these are boot-time allocations that persist for game lifetime ("still reachable")
- [x] **Attempt to fix strdup leaks in race.c:186-190** - add_race() function leaking on race name/desc fields
  - Multiple 4-8 byte leaks during race initialization
  - **Status**: Reviewed (2025-07-26) - these are boot-time allocations that persist for game lifetime ("still reachable")
- [ ] **Review and attempt to fix objsave.c:106 allocation patterns** - objsave_save_obj_record_db() object creation/cleanup

### Low Priority - Code Quality & Infrastructure
- [ ] **Audit entire codebase for strdup usage** - Ensure all strdup() calls have corresponding free()
- [ ] **Create valgrind suppression file** - Filter known false positives from system libraries
- [ ] **Set up automated memory testing** - Add valgrind checks to CI/CD pipeline

### Summary Statistics
- Total leaked: 22,647 bytes definitely lost + 17,228 bytes indirectly lost
- Total allocations: 843,341 allocs, 81,971 frees
- Critical issue: Use-after-free causing crashes needs immediate attention